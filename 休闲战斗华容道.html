<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>华容道</title>
    <script src="https://cdn.jsdelivr.net/npm/phaser@3.60.0/dist/phaser.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            overflow: hidden;
            background-color: #000000;
        }

        #game-container {
            width: 100vw;
            height: 100vh;
        }
         canvas {
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            height: 100vh;
        }
    </style>
</head>

<body>
    <div id="game-container"></div>
    <script id="gameLogic">
        window.onload = function () {
            // 游戏尺寸设置
            const gameWidth = 750  // 最大宽度750px
            const gameHeight = 1334 // 最大高度1334px

            // 战斗系统变量
            let monsters = [], bullets = [], turrets = [], buffBlocks = [];
            let castleHealth = 500, maxCastleHealth = 500;
            let currentBattleLevel = 1, currentWave = 1;
            let battleGrid = [];
            let battleState = 'waiting'; // 改为等待状态，需要点击开始战斗
            let battleTimer = 0;
            let waveTimer = 0;
            let monstersKilled = 0;
            let totalMonstersInWave = 3;
            let battleStarted = false; // 新增：战斗是否已开始

            // 3选1系统变量
            let playerStats = {
                attackDamage: 15,
                attackSpeed: 2000,
                maxHealth: 100,
                critChance: 0,
                lifeSteal: 0,
                multiShot: 1
            };
            let choiceUI = null;
            let choiceOptions = [];
            let isChoosing = false;
            let choiceCounter = 0;
            // 华容道关卡设计
            const levels = [
                // 
               
                {
                    grid: [
                        [7, 0, 0, 0],
                        [1, 1, 2, 2],
                        [1, 1, 2, 2],
                        [0, 4, 0, 0],
                        [3, 0, 5, 6]
                    ],
                    blockSizes: {
                        1: { width: 2, height: 2, type: 'turret' },  // 红色大炮塔
                        2: { width: 2, height: 2, type: 'turret' },  // 蓝色大炮塔
                        3: { width: 1, height: 1, type: 'buff', effect: 'x2' },  // 绿色小方块 x2倍数
                        4: { width: 1, height: 1, type: 'buff', effect: '+2' },  // 黄色小方块 +2伤害
                        5: { width: 1, height: 1, type: 'buff', effect: 'x2' },  // 红色小方块 x2倍数
                        6: { width: 1, height: 1, type: 'buff', effect: 'x4' },  // 蓝色小方块 x4倍数
                        7: { width: 1, height: 1, type: 'buff', effect: '+5' }   // 绿色小方块 +5伤害
                    },
                    // 取消出口系统，炮塔直接射击
                }
            ];
            // 方块颜色（只使用4种颜色）
            const blockColors = [
                0xff6b6b, // 红色
                0x4a6fff, // 蓝色
                0x51cf66, // 绿色
                0xfcc419  // 黄色
            ];

            // 根据方块ID获取颜色的函数
            function getBlockColor(blockId) {
                const colorIndex = (blockId - 1) % blockColors.length;
                return blockColors[colorIndex];
            }
            // 游戏状态变量
            let currentLevel = 0;
            let steps = 0;
            let maxUnlockedLevel = 0;
            // 尝试从本地存储加载最大解锁关卡
            try {
                const savedMaxLevel = localStorage.getItem('huarongdao_maxLevel');
                if (savedMaxLevel !== null) {
                    maxUnlockedLevel = parseInt(savedMaxLevel, 10);
                }
            } catch (e) {
                console.error('无法访问本地存储:', e);
            }
            // 创建Phaser游戏配置
            const config = {
                type: Phaser.AUTO,
                width: 750,
                height: 1334,
                parent: 'game-container',
                backgroundColor: '#1a1a2e',
                scene: {
                    preload: preload,
                    create: create,
                    update: update
                },
                scale: {
                    mode: Phaser.Scale.FIT,
                    autoCenter: Phaser.Scale.CENTER_BOTH
                }
            };
            // 创建游戏实例
            const game = new Phaser.Game(config);
            // 游戏变量
            let gameScene;
            let blocks = [];
            let exits = [];
            let cellSize;
            let boardX;
            let boardY;
            let boardWidth;
            let boardHeight;
            let grid;
            let currentBlockSizes;
            // 删除了stepText和levelText变量，简化UI
            let selectedBlock = null;
            let startDragPosition = null;

            function preload() {
                // 不需要预加载纹理，使用emoji和文字
            }
            function create() {
                gameScene = this;

                // 创建战斗系统（上半部分）
                createBattleSystem();

                // 设置华容道游戏布局（下半部分）
                setupGameLayout();
                // 加载当前关卡
                loadLevel(currentLevel);
                // 创建UI元素
                createUI();
            }
            function update(time, delta) {
                // 更新战斗系统
                updateBattleSystem(time, delta);
                // 华容道游戏更新逻辑
            }

            function createBattleSystem() {
                // 添加战斗背景
                const battleAreaHeight = gameHeight * 0.4;
                const battleBg = gameScene.add.rectangle(gameWidth / 2, battleAreaHeight / 2, gameWidth, battleAreaHeight, 0x2c3e50);

                // 不再创建城墙和塔防位置

                // 存储战斗区域信息
                gameScene.battleAreaHeight = battleAreaHeight;

                // 创建战斗UI
                createBattleUI();

                // 不自动创建怪物，等待开始战斗按钮
            }

            function createBattleUI() {
                const battleAreaHeight = gameHeight * 0.4;

                // 城堡血条背景
                gameScene.add.rectangle(gameWidth / 2, 50, 300, 20, 0x333333);
                // 城堡血条
                gameScene.defenseWallHealthBar = gameScene.add.rectangle(gameWidth / 2, 50, 300, 20, 0x4A6FFF);
                gameScene.defenseWallHealthBar.setOrigin(0.5, 0.5);

                // UI文本
                gameScene.battleLevelText = gameScene.add.text(50, 80, `战斗关卡: ${currentBattleLevel}`, {
                    fontSize: '16px',
                    fill: '#ffffff'
                });
                gameScene.battleWaveText = gameScene.add.text(200, 80, `波次: ${currentWave}`, {
                    fontSize: '16px',
                    fill: '#ffffff'
                });
                gameScene.defenseWallHealthText = gameScene.add.text(350, 80, `城堡: ${castleHealth}/${maxCastleHealth}`, {
                    fontSize: '16px',
                    fill: '#ffffff'
                });
                gameScene.allyCountText = gameScene.add.text(550, 80, `守军: 0`, {
                    fontSize: '16px',
                    fill: '#2ecc71'
                });

                // 创建开始战斗按钮
                const startBattleButton = gameScene.add.rectangle(gameWidth / 2, battleAreaHeight - 50, 200, 60, 0x27ae60);
                startBattleButton.setStrokeStyle(3, 0x2ecc71);
                startBattleButton.setInteractive();

                const startBattleText = gameScene.add.text(gameWidth / 2, battleAreaHeight - 50, '开始战斗', {
                    fontSize: '24px',
                    fill: '#ffffff',
                    fontWeight: 'bold'
                }).setOrigin(0.5);

                // 按钮点击事件
                startBattleButton.on('pointerdown', () => {
                    if (!battleStarted) {
                        startBattle();
                        startBattleButton.setVisible(false);
                        startBattleText.setVisible(false);
                    }
                });

                // 按钮悬停效果
                startBattleButton.on('pointerover', () => {
                    startBattleButton.setFillStyle(0x2ecc71);
                });
                startBattleButton.on('pointerout', () => {
                    startBattleButton.setFillStyle(0x27ae60);
                });

                gameScene.startBattleButton = startBattleButton;
                gameScene.startBattleText = startBattleText;

                // 怪物血条容器
                gameScene.monsterHealthBars = [];
            }

            // 新增：开始战斗函数
            function startBattle() {
                battleStarted = true;
                battleState = 'playing';

                // 创建怪物
                createBattleWave();

                // 激活所有炮塔开始射击
                turrets.forEach(turret => {
                    turret.isActive = true;
                });
            }

            // 新增：创建子弹函数
            function createBullet(turret, target) {
                const bullet = gameScene.add.circle(turret.x, turret.y, 5, 0xFFFF00);
                bullet.damage = turret.baseDamage;
                bullet.speed = 300; // 像素/秒
                bullet.target = target;
                bullet.turret = turret;
                bullet.hasPassedBuffs = []; // 记录已经经过的增益方块

                bullets.push(bullet);
                return bullet;
            }

            // 新增：更新子弹函数
            function updateBullets(time, delta) {
                bullets.forEach((bullet, index) => {
                    if (!bullet.target || bullet.target.health <= 0) {
                        bullet.destroy();
                        bullets.splice(index, 1);
                        return;
                    }

                    // 计算子弹移动
                    const angle = Phaser.Math.Angle.Between(bullet.x, bullet.y, bullet.target.x, bullet.target.y);
                    const speed = bullet.speed * (delta / 1000);
                    bullet.x += Math.cos(angle) * speed;
                    bullet.y += Math.sin(angle) * speed;

                    // 检查是否经过增益方块
                    buffBlocks.forEach(buffBlock => {
                        if (!bullet.hasPassedBuffs.includes(buffBlock.id)) {
                            const distance = Phaser.Math.Distance.Between(bullet.x, bullet.y, buffBlock.x, buffBlock.y);
                            if (distance < 30) { // 经过增益方块
                                bullet.hasPassedBuffs.push(buffBlock.id);
                                applyBulletBuff(bullet, buffBlock);
                            }
                        }
                    });

                    // 检查是否击中目标
                    const distanceToTarget = Phaser.Math.Distance.Between(bullet.x, bullet.y, bullet.target.x, bullet.target.y);
                    if (distanceToTarget < 20) {
                        // 击中目标
                        bullet.target.health -= bullet.damage;

                        // 击中特效
                        const hitEffect = gameScene.add.circle(bullet.x, bullet.y, 15, 0xFF0000, 0.7);
                        gameScene.tweens.add({
                            targets: hitEffect,
                            alpha: 0,
                            scaleX: 2,
                            scaleY: 2,
                            duration: 200,
                            onComplete: () => hitEffect.destroy()
                        });

                        // 检查目标是否死亡
                        if (bullet.target.health <= 0) {
                            bullet.target.destroy();
                            const monsterIndex = monsters.indexOf(bullet.target);
                            if (monsterIndex > -1) {
                                monsters.splice(monsterIndex, 1);
                                monstersKilled++;
                            }
                        }

                        bullet.destroy();
                        bullets.splice(index, 1);
                    }
                });
            }

            // 新增：应用子弹增益效果
            function applyBulletBuff(bullet, buffBlock) {
                const effect = buffBlock.effect;

                // 显示增益效果
                const buffText = gameScene.add.text(buffBlock.x, buffBlock.y - 20, effect, {
                    fontSize: '16px',
                    fill: '#FFD700',
                    fontWeight: 'bold'
                }).setOrigin(0.5);

                gameScene.tweens.add({
                    targets: buffText,
                    alpha: 0,
                    y: buffText.y - 20,
                    duration: 1000,
                    onComplete: () => buffText.destroy()
                });

                // 应用效果
                if (effect.startsWith('x')) {
                    const multiplier = parseInt(effect.substring(1));
                    bullet.damage *= multiplier;
                    // 叠加颜色效果 - 如果已经有颜色变化，保持更强的效果
                    if (bullet.fillColor === 0xFFFF00) { // 原始黄色
                        bullet.setFillStyle(0xFF6600); // 变成橙色表示倍数增益
                    } else if (bullet.fillColor === 0x00FF00) { // 如果是绿色（+伤害），变成紫色表示混合效果
                        bullet.setFillStyle(0xFF00FF); // 紫色表示倍数+伤害混合
                    }
                } else if (effect.startsWith('+')) {
                    const addValue = parseInt(effect.substring(1));

                    // 特殊处理：+5 效果会分裂子弹
                    if (addValue === 5) {
                        // 创建4个额外的子弹（总共5个）
                        for (let i = 1; i < 5; i++) {
                            const newBullet = gameScene.add.circle(bullet.x, bullet.y, bullet.radius, bullet.fillColor);
                            newBullet.damage = bullet.damage + addValue; // 继承当前伤害并增加
                            newBullet.speed = bullet.speed;
                            newBullet.target = bullet.target;
                            newBullet.turret = bullet.turret;
                            newBullet.hasPassedBuffs = [...bullet.hasPassedBuffs]; // 复制已经经过的增益

                            // 给新子弹一个稍微不同的角度，形成扇形散射
                            const angleOffset = (i - 2) * 0.2; // -0.4, -0.2, 0.2, 0.4 弧度的偏移
                            newBullet.angleOffset = angleOffset;

                            bullets.push(newBullet);

                            // 分裂特效
                            const splitEffect = gameScene.add.circle(bullet.x, bullet.y, 10, 0x00FFFF, 0.7);
                            gameScene.tweens.add({
                                targets: splitEffect,
                                alpha: 0,
                                scaleX: 3,
                                scaleY: 3,
                                duration: 300,
                                onComplete: () => splitEffect.destroy()
                            });
                        }
                    }

                    bullet.damage += addValue;
                    // 叠加颜色效果
                    if (bullet.fillColor === 0xFFFF00) { // 原始黄色
                        bullet.setFillStyle(0x00FF00); // 变成绿色表示伤害增益
                    } else if (bullet.fillColor === 0xFF6600) { // 如果是橙色（倍数），变成紫色表示混合效果
                        bullet.setFillStyle(0xFF00FF); // 紫色表示倍数+伤害混合
                    }
                }

                // 子弹变大表示增强（叠加效果）
                bullet.setRadius(bullet.radius + 1);
            }

            function createBattleWave() {
                // 清除现有怪物（但保留我方方块）
                monsters.forEach(monster => monster.destroy());
                monsters = [];

                // 创建新怪物 - 从上方生成
                for (let i = 0; i < totalMonstersInWave; i++) {
                    const xPos = 100 + i * 120; // 水平分布
                    const yPos = 30; // 从顶部生成

                    // 随机选择怪物emoji
                    const monsterEmojis = ['👹', '👺', '🧌', '👻', '💀', '🧟', '🧛', '🐺', '🦇', '🕷️'];
                    const monsterEmoji = monsterEmojis[Math.floor(Math.random() * monsterEmojis.length)];

                    let monster = gameScene.add.text(xPos, yPos, monsterEmoji, {
                        fontSize: '32px'
                    });
                    monster.setOrigin(0.5, 1);

                    monster.health = 30 + currentBattleLevel * 10;
                    monster.maxHealth = monster.health;
                    monster.lastAttack = 0;
                    monster.isRanged = false;
                    monster.isMoving = false;
                    monster.originalX = xPos;
                    monster.originalY = yPos;
                    monster.jumpTween = null;
                    monster.isNewlySpawned = true; // 标记为新生成
                    monsters.push(monster);

                    // 怪物生成闪动效果
                    monster.setAlpha(0);
                    gameScene.tweens.add({
                        targets: monster,
                        alpha: 1,
                        duration: 300,
                        ease: 'Power2',
                        repeat: 2,
                        yoyo: true,
                        onComplete: () => {
                            monster.setAlpha(1);
                            // 闪动完成后，允许被攻击
                            setTimeout(() => {
                                monster.isNewlySpawned = false;
                            }, 500);
                        }
                    });
                }

                monstersKilled = 0;

                // 重置所有炮塔的射击状态
                turrets.forEach(turret => {
                    turret.lastShot = 0; // 重置射击冷却
                });
            }

            function updateBattleSystem(time, delta) {
                // 始终更新战斗UI
                updateBattleUI();

                if (battleState === 'playing' && battleStarted) {
                    // 怪物AI
                    updateMonsters(time, delta);

                    // 更新炮塔射击
                    updateTurrets(time, delta);

                    // 更新子弹
                    updateBullets(time, delta);

                    // 检查波次完成
                    checkBattleWaveComplete();

                    // 检查游戏结束
                    if (castleHealth <= 0) {
                        battleState = 'gameOver';
                        gameScene.add.text(gameWidth / 2, gameHeight * 0.2, '城堡被摧毁！游戏结束', {
                            fontSize: '32px',
                            fill: '#e74c3c',
                            fontFamily: 'Arial'
                        }).setOrigin(0.5);

                        // 显示重新开始按钮
                        const restartButton = gameScene.add.rectangle(gameWidth / 2, gameHeight * 0.3, 200, 60, 0x27ae60);
                        restartButton.setStrokeStyle(3, 0x2ecc71);
                        restartButton.setInteractive();

                        const restartText = gameScene.add.text(gameWidth / 2, gameHeight * 0.3, '重新开始', {
                            fontSize: '24px',
                            fill: '#ffffff',
                            fontWeight: 'bold'
                        }).setOrigin(0.5);

                        restartButton.on('pointerdown', () => {
                            // 重新加载页面
                            location.reload();
                        });
                    }
                }
            }

            // 删除了playerAttack函数，现在只有我方方块攻击

            // 新增：更新炮塔射击
            function updateTurrets(time, delta) {
                turrets.forEach(turret => {
                    if (!turret.isActive) return;

                    // 寻找最近的怪物
                    let nearestMonster = null;
                    let nearestDistance = Infinity;

                    monsters.forEach(monster => {
                        if (monster.isNewlySpawned) return;
                        const distance = Phaser.Math.Distance.Between(turret.x, turret.y, monster.x, monster.y);
                        if (distance < nearestDistance && distance < turret.range) {
                            nearestDistance = distance;
                            nearestMonster = monster;
                        }
                    });

                    // 射击
                    if (nearestMonster && time - turret.lastShot > turret.fireRate) {
                        createBullet(turret, nearestMonster);
                        turret.lastShot = time;

                        // 射击动画
                        gameScene.tweens.add({
                            targets: turret.barrel,
                            scaleX: 1.2,
                            scaleY: 1.2,
                            duration: 100,
                            yoyo: true
                        });
                    }
                });
            }

            // 创建炮塔和增益方块
            function createTurretOrBuff(blockContainer, blockId, color, blockX, blockY, blockWidth, blockHeight) {
                const blockSize = currentBlockSizes[blockId];
                const centerX = blockX + blockWidth / 2;
                const centerY = blockY + blockHeight / 2;

                if (blockSize.type === 'turret') {
                    // 创建炮塔
                    const turret = {
                        id: blockId,
                        x: centerX,
                        y: centerY,
                        color: color,
                        baseDamage: 20,
                        range: 700,
                        fireRate: 1000, // 1秒射击一次
                        lastShot: 0,
                        isActive: false,
                        blockContainer: blockContainer
                    };

                    // 创建炮管
                    const barrel = gameScene.add.rectangle(centerX, centerY - 15, 8, 30, 0x666666);
                    turret.barrel = barrel;

                    // 添加炮塔标识
                    const turretIcon = gameScene.add.text(centerX, centerY + 10, '🔫', {
                        fontSize: '24px'
                    }).setOrigin(0.5);
                    turret.icon = turretIcon;

                    turrets.push(turret);
                } else if (blockSize.type === 'buff') {
                    // 创建增益方块
                    const buffBlock = {
                        id: blockId,
                        x: centerX,
                        y: centerY,
                        color: color,
                        effect: blockSize.effect,
                        blockContainer: blockContainer
                    };

                    // 添加增益效果文字（放大3倍）
                    const buffText = gameScene.add.text(centerX, centerY, blockSize.effect, {
                        fontSize: '54px', // 18px * 3 = 54px
                        fill: '#FFD700',
                        fontWeight: 'bold',
                        stroke: '#000000',
                        strokeThickness: 6 // 也放大描边
                    }).setOrigin(0.5);
                    buffBlock.text = buffText;

                    buffBlocks.push(buffBlock);
                }
            }

            function createAllyBlocksOnBlock(color, count, exitSide, spawnX, spawnY, blockId) {
                for (let i = 0; i < count; i++) {
                    // 在华容道方块上生成（稍微分散避免重叠）
                    const x = spawnX + (i % 2) * 20 - 10; // 2x2排列，更紧密
                    const y = spawnY + Math.floor(i / 2) * 20 - 10;

                    // 创建我方方块
                    const allyBlock = gameScene.add.rectangle(x, y, 30, 30, color);
                    allyBlock.setStrokeStyle(2, 0xffffff);

                    // 添加士兵emoji
                    const soldierEmojis = ['⚔️', '🛡️', '🏹', '🗡️'];
                    const soldierEmoji = soldierEmojis[i % soldierEmojis.length];
                    const soldierText = gameScene.add.text(x, y, soldierEmoji, {
                        fontSize: '20px'
                    });
                    soldierText.setOrigin(0.5, 0.5);

                    // 将emoji绑定到方块
                    allyBlock.soldierText = soldierText;

                    // 设置我方方块属性
                    allyBlock.health = 50;
                    allyBlock.maxHealth = 50;
                    allyBlock.attackDamage = 15;
                    allyBlock.lastAttack = 0;
                    allyBlock.targetMonster = null;
                    allyBlock.isMoving = false;
                    allyBlock.originalColor = color;
                    allyBlock.exitSide = exitSide; // 出城方向
                    allyBlock.hasExitedGate = false; // 是否已经出城门
                    allyBlock.hasReachedGate = false; // 是否已经到达城门
                    allyBlock.movementPhase = 'waiting'; // 等待方块出去后开始移动
                    allyBlock.waitingForBlockExit = true; // 等待方块出去
                    allyBlock.onBlockId = blockId; // 记录站在哪个方块上
                    allyBlock.exitSide = exitSide; // 记录方块的出口方向

                    // 添加到我方方块数组
                    allyBlocks.push(allyBlock);

                    // 创建入场动画
                    allyBlock.setScale(0);
                    soldierText.setScale(0);
                    gameScene.tweens.add({
                        targets: [allyBlock, soldierText],
                        scaleX: 1,
                        scaleY: 1,
                        duration: 500,
                        ease: 'Back.easeOut'
                    });

                    // 显示创建提示
                    const createText = gameScene.add.text(x, y - 25, '+1 守军', {
                        fontSize: '10px',
                        fill: '#2ecc71',
                        fontFamily: 'Arial',
                        fontWeight: 'bold'
                    }).setOrigin(0.5);

                    gameScene.tweens.add({
                        targets: createText,
                        alpha: 0,
                        y: createText.y - 15,
                        duration: 1500,
                        onComplete: () => createText.destroy()
                    });
                }
            }

            function updateMonsters(time, delta) {
                monsters.forEach((monster, index) => {
                    // 新生成的怪物在闪动期间不行动
                    if (monster.isNewlySpawned) return;

                    const battleAreaHeight = gameScene.battleAreaHeight;
                    const castleY = battleAreaHeight - 20; // 城堡位置在战斗区域底部

                    // 向下移动攻击城堡
                    if (monster.y < castleY - 50 && !monster.isMoving) {
                        monster.isMoving = true;

                        // 向城堡移动
                        const targetX = monster.x + Math.random() * 60 - 30; // 稍微偏移
                        const targetY = castleY - 40; // 移动到城堡附近

                        gameScene.tweens.add({
                            targets: monster,
                            x: targetX,
                            y: targetY,
                            duration: 4000,
                            ease: 'Power2',
                            onComplete: () => {
                                monster.isMoving = false;
                            }
                        });
                    }

                    // 计算到城堡的距离
                    const distanceToCastle = Phaser.Math.Distance.Between(monster.x, monster.y, gameWidth / 2, castleY);

                    // 攻击城堡
                    if (distanceToCastle <= 120 && time - monster.lastAttack > 2000) {
                        let damage = 12 + currentBattleLevel;
                        castleHealth -= damage;
                        monster.lastAttack = time;

                        // 攻击动画
                        gameScene.tweens.add({
                            targets: monster,
                            rotation: 0.4,
                            duration: 150,
                            yoyo: true,
                            ease: 'Power2'
                        });

                        // 城堡受击效果（血条闪红）
                        gameScene.tweens.add({
                            targets: gameScene.defenseWallHealthBar,
                            fillColor: 0xff0000,
                            duration: 100,
                            yoyo: true,
                            onComplete: () => {
                                const healthPercent = castleHealth / maxCastleHealth;
                                gameScene.defenseWallHealthBar.fillColor = healthPercent > 0.5 ? 0x4A6FFF :
                                                                    healthPercent > 0.25 ? 0xf39c12 : 0xe74c3c;
                            }
                        });
                    }

                    // 如果怪物到达城堡附近但没有在攻击，强制开始攻击
                    else if (monster.y >= castleY - 60 && time - monster.lastAttack > 3000) {
                        let damage = 8 + currentBattleLevel;
                        castleHealth -= damage;
                        monster.lastAttack = time;

                        // 简单攻击动画
                        gameScene.tweens.add({
                            targets: monster,
                            scaleX: 1.2,
                            scaleY: 1.2,
                            duration: 100,
                            yoyo: true
                        });
                    }
                });
            }

            // 删除士兵更新逻辑，现在使用炮塔和子弹系统

            function updateBattleUI() {
                // 更新防御墙血条
                const healthPercent = castleHealth / maxCastleHealth;
                gameScene.defenseWallHealthBar.scaleX = healthPercent;
                gameScene.defenseWallHealthBar.x = (gameWidth / 2) - 150 + (300 * healthPercent) / 2;
                gameScene.defenseWallHealthBar.fillColor = healthPercent > 0.5 ? 0x4A6FFF :
                                                    healthPercent > 0.25 ? 0xf39c12 : 0xe74c3c;

                // 更新文本
                gameScene.battleLevelText.setText(`战斗关卡: ${currentBattleLevel}`);
                gameScene.battleWaveText.setText(`波次: ${currentWave}`);
                gameScene.defenseWallHealthText.setText(`城堡: ${Math.max(0, castleHealth)}/${maxCastleHealth}`);
                gameScene.allyCountText.setText(`炮塔: ${turrets.length} | 子弹: ${bullets.length}`);

                // 更新怪物血条
                gameScene.monsterHealthBars.forEach(bar => bar.destroy());
                gameScene.monsterHealthBars = [];

                monsters.forEach((monster, index) => {
                    const healthPercent = monster.health / monster.maxHealth;

                    // 血条背景
                    const barBg = gameScene.add.rectangle(
                        monster.x,
                        monster.y - 40,
                        30,
                        4,
                        0x333333
                    );
                    gameScene.monsterHealthBars.push(barBg);

                    // 血条前景
                    const bar = gameScene.add.rectangle(
                        monster.x - 15 + (30 * healthPercent) / 2,
                        monster.y - 40,
                        30 * healthPercent,
                        4,
                        healthPercent > 0.5 ? 0x27ae60 : 0xe74c3c
                    );
                    gameScene.monsterHealthBars.push(bar);
                });

                // 不再显示士兵血条，现在是炮塔系统
            }

            function checkBattleWaveComplete() {
                if (monsters.length === 0 && !isChoosing) {
                    currentWave++;

                    if (currentWave > 3) {
                        currentBattleLevel++;
                        currentWave = 1;
                        totalMonstersInWave = Math.min(6, 3 + Math.floor(currentBattleLevel / 2));
                    }

                    // 创建新波次
                    setTimeout(() => {
                        createBattleWave();
                    }, 1000);
                }
            }
            function setupGameLayout() {
                // 华容道区域在下半部分，战斗系统占用上半部分
                const battleAreaHeight = gameHeight * 0.4; // 战斗系统占40%
                const headerHeight = gameHeight * 0.05; // 华容道标题区域
                const footerHeight = gameHeight * 0.05; // 底部空间
                boardHeight = gameHeight - battleAreaHeight - headerHeight - footerHeight;
                // 华容道是4列5行的网格
                const cols = 4;
                const rows = 5;
                // 计算单元格大小，确保游戏板能完全适应屏幕，增加利用率
                cellSize = Math.min(gameWidth / cols, boardHeight / rows) * 0.90;
                // 计算游戏板的宽度和位置，使其居中
                boardWidth = cellSize * cols;
                boardX = (gameWidth - boardWidth) / 2;
                boardY = battleAreaHeight + headerHeight + (boardHeight - cellSize * rows) / 2;

                // 绘制华容道游戏板背景
                gameScene.add.rectangle(gameWidth / 2, battleAreaHeight + headerHeight + boardHeight / 2, boardWidth + cellSize * 0.1, cellSize * rows + cellSize * 0.1, 0xEEEEEE)
                   .setOrigin(0.5, 0.5)
                   .setStrokeStyle(2, 0xDDDDDD);

                // 不再绘制马路和城墙系统
            }
            function loadLevel(levelIndex) {
                // 清除现有方块和出口
                blocks.forEach(block => block.destroy());
                blocks = [];
                exits.forEach(exit => {
                    exit.graphics.destroy();
                    if (exit.multiplierText) {
                        exit.multiplierText.destroy();
                    }
                });
                exits = [];
                // 重置步数
                steps = 0;
                // 重置战斗状态
                battleStarted = false;
                battleState = 'waiting';
                // 清除所有炮塔和增益方块
                turrets.forEach(turret => {
                    if (turret.barrel) turret.barrel.destroy();
                    if (turret.icon) turret.icon.destroy();
                });
                turrets = [];
                buffBlocks.forEach(buff => {
                    if (buff.text) buff.text.destroy();
                });
                buffBlocks = [];
                // 清除所有子弹
                bullets.forEach(bullet => bullet.destroy());
                bullets = [];
                // 获取当前关卡数据
                const levelData = levels[levelIndex];
                grid = JSON.parse(JSON.stringify(levelData.grid)); // 深拷贝网格
                currentBlockSizes = levelData.blockSizes;
                // 不再创建出口，因为炮塔直接射击
                // 创建方块
                createBlocks();
                // 显示开始战斗按钮
                if (gameScene.startBattleButton) {
                    gameScene.startBattleButton.setVisible(true);
                    gameScene.startBattleText.setVisible(true);
                }
                // UI已简化，不更新华容道相关文本
            }
            function createExits(exitData) {
                exitData.forEach(exit => {
                    const { side, col, row, width, color, multiplier } = exit;
                    let x, y, exitWidth, exitHeight;

                    if (side === 'top') {
                        x = boardX + col * cellSize;
                        y = boardY - cellSize * 0.3; // 出口更窄
                        exitWidth = width * cellSize;
                        exitHeight = cellSize * 0.3;
                    } else if (side === 'bottom') {
                        x = boardX + col * cellSize;
                        y = boardY + 5 * cellSize;
                        exitWidth = width * cellSize;
                        exitHeight = cellSize * 0.3;
                    } else if (side === 'left') {
                        x = boardX - cellSize * 0.3;
                        y = boardY + row * cellSize;
                        exitWidth = cellSize * 0.3;
                        exitHeight = width * cellSize; // 这里width表示高度
                    } else if (side === 'right') {
                        x = boardX + 4 * cellSize;
                        y = boardY + row * cellSize;
                        exitWidth = cellSize * 0.3;
                        exitHeight = width * cellSize;
                    }

                    const exitBlock = gameScene.add.graphics();
                    exitBlock.fillStyle(color, 1);
                    exitBlock.fillRect(x, y, exitWidth, exitHeight);

                    // 添加倍数文字显示
                    const multiplierText = gameScene.add.text(
                        x + exitWidth / 2,
                        y + exitHeight / 2,
                        `x${multiplier}`,
                        {
                            fontSize: '18px',
                            fill: '#ffffff',
                            fontWeight: 'bold',
                            stroke: '#000000',
                            strokeThickness: 2
                        }
                    ).setOrigin(0.5);

                    exits.push({
                        graphics: exitBlock,
                        multiplierText: multiplierText,
                        side,
                        col: col || 0,
                        row: row || 0,
                        width: width || 1,
                        color,
                        blockId: exit.blockId,
                        multiplier: multiplier || 1
                    });
                });
            }
            function createBlocks() {
                // 创建方块对象
                const blockIds = new Set();
                // 首先找出所有方块ID
                for (let row = 0; row < grid.length; row++) {
                    for (let col = 0; col < grid[row].length; col++) {
                        const blockId = grid[row][col];
                        if (blockId > 0) {
                            blockIds.add(blockId);
                        }
                    }
                }
                // 为每个方块ID创建方块
                blockIds.forEach(blockId => {
                    // 找到方块的起始位置
                    let startRow = -1, startCol = -1;
                    outerLoop: for (let row = 0; row < grid.length; row++) {
                        for (let col = 0; col < grid[row].length; col++) {
                            if (grid[row][col] === blockId) {
                                startRow = row;
                                startCol = col;
                                break outerLoop;
                            }
                        }
                    }
                    if (startRow >= 0 && startCol >= 0) {
                        const blockSize = currentBlockSizes[blockId];
                        const width = blockSize.width;
                        const height = blockSize.height;
                        // 计算方块的像素位置和大小
                        const x = boardX + startCol * cellSize;
                        const y = boardY + startRow * cellSize;
                        const blockWidth = width * cellSize;
                        const blockHeight = height * cellSize;
                        // 选择方块颜色
                        const colorIndex = (blockId - 1) % blockColors.length;
                        const color = blockColors[colorIndex];
                        // 创建方块图形
                        const block = gameScene.add.graphics();
                        // 绘制方块
                        block.fillStyle(color, 1);
                        block.lineStyle(2, 0xffffff, 1);
                        block.fillRoundedRect(0, 0, blockWidth - 4, blockHeight - 4, 10);
                        block.strokeRoundedRect(0, 0, blockWidth - 4, blockHeight - 4, 10);
                        // 创建方块容器
                        const blockContainer = gameScene.add.container(x + 2, y + 2);
                        blockContainer.add(block);
                        // 存储方块数据
                        blockContainer.setData('id', blockId);
                        blockContainer.setData('row', startRow);
                        blockContainer.setData('col', startCol);
                        blockContainer.setData('width', width);
                        blockContainer.setData('height', height);
                        blockContainer.setData('color', color);
                        // 使方块可交互
                        blockContainer.setInteractive(new Phaser.Geom.Rectangle(0, 0, blockWidth - 4, blockHeight - 4), Phaser.Geom.Rectangle.Contains);
                        // 添加拖动事件
                        gameScene.input.setDraggable(blockContainer);
                        // 将方块添加到数组
                        blocks.push(blockContainer);

                        // 创建炮塔或增益方块
                        createTurretOrBuff(blockContainer, blockId, color, x, y, blockWidth, blockHeight);
                    }
                });
                // 设置拖动事件
                gameScene.input.on('dragstart', function (pointer, gameObject) {
                    selectedBlock = gameObject;
                    startDragPosition = { x: pointer.x, y: pointer.y };
                });
                gameScene.input.on('drag', function (pointer, gameObject, dragX, dragY) {
                    // 不直接更新位置，只记录拖动方向
                });
                gameScene.input.on('dragend', function (pointer, gameObject) {
                    if (startDragPosition) {
                        const dx = pointer.x - startDragPosition.x;
                        const dy = pointer.y - startDragPosition.y;
                        // 确定主要拖动方向
                        if (Math.abs(dx) > Math.abs(dy)) {
                            // 水平拖动
                            if (dx > cellSize / 3) {
                                moveBlockToEnd(gameObject, 'right');
                            } else if (dx < -cellSize / 3) {
                                moveBlockToEnd(gameObject, 'left');
                            }
                        } else {
                            // 垂直拖动
                            if (dy > cellSize / 3) {
                                moveBlockToEnd(gameObject, 'down');
                            } else if (dy < -cellSize / 3) {
                                moveBlockToEnd(gameObject, 'up');
                            }
                        }
                        startDragPosition = null;
                    }
                    selectedBlock = null;
                });
                // 添加点击事件
                gameScene.input.on('gameobjectdown', function (pointer, gameObject) {
                    selectedBlock = gameObject;
                });
            }
            function moveBlockToEnd(block, direction) {
                const blockId = block.getData('id');
                const startRow = block.getData('row');
                const startCol = block.getData('col');
                const width = block.getData('width');
                const height = block.getData('height');

                let newRow = startRow;
                let newCol = startCol;

                // 找到该方向上能移动的最远位置
                let canMove = true;
                while (canMove) {
                    let testRow = newRow;
                    let testCol = newCol;

                    // 根据方向计算下一个测试位置
                    switch (direction) {
                        case 'up':
                            testRow--;
                            break;
                        case 'down':
                            testRow++;
                            break;
                        case 'left':
                            testCol--;
                            break;
                        case 'right':
                            testCol++;
                            break;
                    }

                    // 检查这个位置是否有效
                    if (isValidMove(blockId, startRow, startCol, testRow, testCol, width, height)) {
                        newRow = testRow;
                        newCol = testCol;
                    } else {
                        canMove = false;
                    }
                }

                // 如果找到了新位置，执行移动
                if (newRow !== startRow || newCol !== startCol) {
                    // 更新网格
                    updateGrid(blockId, startRow, startCol, newRow, newCol, width, height);
                    // 更新方块位置
                    block.setData('row', newRow);
                    block.setData('col', newCol);

                    // 移动方块上的炮塔和增益方块
                    moveBlockElements(block, startRow, startCol, newRow, newCol);

                    // 动画移动方块
                    gameScene.tweens.add({
                        targets: block,
                        x: boardX + newCol * cellSize + 2,
                        y: boardY + newRow * cellSize + 2,
                        duration: 300, // 稍微长一点的动画时间
                        ease: 'Power2',
                        onComplete: function() {
                            if (!gameScene || !gameScene.scene || !gameScene.scene.isActive()) return;
                            // 不再检查出口条件
                            // 检查是否过关
                            checkWinCondition();
                        }
                    });
                    // 增加步数（不显示）
                    steps++;
                } else {
                    // 无效移动，播放轻微震动动画
                    gameScene.tweens.add({
                        targets: block,
                        x: block.x + (direction === 'right' ? 5 : (direction === 'left' ? -5 : 0)),
                        y: block.y + (direction === 'down' ? 5 : (direction === 'up' ? -5 : 0)),
                        duration: 50,
                        yoyo: true,
                        repeat: 1
                    });
                }
            }

            // 移动方块上的炮塔和增益方块
            function moveBlockElements(block, startRow, startCol, newRow, newCol) {
                const blockId = block.getData('id');
                const deltaX = (newCol - startCol) * cellSize;
                const deltaY = (newRow - startRow) * cellSize;

                // 移动炮塔
                turrets.forEach(turret => {
                    if (turret.id === blockId) {
                        turret.x += deltaX;
                        turret.y += deltaY;
                        if (turret.barrel) {
                            gameScene.tweens.add({
                                targets: turret.barrel,
                                x: turret.barrel.x + deltaX,
                                y: turret.barrel.y + deltaY,
                                duration: 300,
                                ease: 'Power2'
                            });
                        }
                        if (turret.icon) {
                            gameScene.tweens.add({
                                targets: turret.icon,
                                x: turret.icon.x + deltaX,
                                y: turret.icon.y + deltaY,
                                duration: 300,
                                ease: 'Power2'
                            });
                        }
                    }
                });

                // 移动增益方块
                buffBlocks.forEach(buff => {
                    if (buff.id === blockId) {
                        buff.x += deltaX;
                        buff.y += deltaY;
                        if (buff.text) {
                            gameScene.tweens.add({
                                targets: buff.text,
                                x: buff.text.x + deltaX,
                                y: buff.text.y + deltaY,
                                duration: 300,
                                ease: 'Power2'
                            });
                        }
                    }
                });
            }

            // 删除了塔防相关函数，现在士兵直接在战斗区域战斗
            function isValidMove(blockId, startRow, startCol, newRow, newCol, width, height) {
                // 检查是否超出边界
                if (newRow < 0 || newCol < 0 || newRow + height > grid.length || newCol + width > grid[0].length) {
                    return false;
                }
                // 检查新位置是否被占用
                for (let r = 0; r < height; r++) {
                    for (let c = 0; c < width; c++) {
                        const currentCell = grid[startRow + r][startCol + c];
                        const newCell = grid[newRow + r][newCol + c];
                        // 如果新位置不是当前方块或空位，则无法移动
                        if (newCell !== 0 && newCell !== blockId) {
                            return false;
                        }
                    }
                }
                return true;
            }
            function updateGrid(blockId, startRow, startCol, newRow, newCol, width, height) {
                // 清除原位置
                for (let r = 0; r < height; r++) {
                    for (let c = 0; c < width; c++) {
                        if (grid[startRow + r][startCol + c] === blockId) {
                            grid[startRow + r][startCol + c] = 0;
                        }
                    }
                }
                // 设置新位置
                for (let r = 0; r < height; r++) {
                    for (let c = 0; c < width; c++) {
                        grid[newRow + r][newCol + c] = blockId;
                    }
                }
            }
            function checkExitCondition(block) {
                const blockId = block.getData('id');
                const row = block.getData('row');
                const col = block.getData('col');
                const width = block.getData('width');
                const height = block.getData('height');
                const blockColor = block.getData('color');
                
                levels[currentLevel].exits.forEach(exit => {
                    const { side, col: exitCol, width: exitWidth, color, blockId: exitBlockId } = exit;

                    // 检查方块是否到达出口位置
                    if (side === 'top' && row === 0) {
                        // 检查方块是否在出口范围内，且方块宽度不超过出口宽度
                        if (col >= exitCol && col + width <= exitCol + exitWidth && width <= exitWidth) {
                            // 检查方块颜色是否与出口匹配
                            if (blockColor === color || blockId === exitBlockId) {
                                // 执行出门动画
                                executeExitAnimation(block, side, exitCol);
                                return;
                            } else {
                                // 颜色不匹配，播放错误动画
                                gameScene.tweens.add({
                                    targets: block,
                                    alpha: 0.5,
                                    duration: 200,
                                    yoyo: true,
                                    repeat: 1
                                });
                                return;
                            }
                        }
                    } else if (side === 'bottom' && row + height === 5) {
                        // 检查方块是否在出口范围内，且方块宽度不超过出口宽度
                        if (col >= exitCol && col + width <= exitCol + exitWidth && width <= exitWidth) {
                            // 检查方块颜色是否与出口匹配
                            if (blockColor === color || blockId === exitBlockId) {
                                // 执行出门动画
                                executeExitAnimation(block, side, exitCol);
                                return;
                            } else {
                                // 颜色不匹配，播放错误动画
                                gameScene.tweens.add({
                                    targets: block,
                                    alpha: 0.5,
                                    duration: 200,
                                    yoyo: true,
                                    repeat: 1
                                });
                                return;
                            }
                        }
                    }
                });
            }
            
            function executeExitAnimation(block, side, exitCol) {
                const blockId = block.getData('id');
                const width = block.getData('width');
                const height = block.getData('height');
                
                // 首先播放闪烁效果
                gameScene.tweens.add({
                    targets: block,
                    alpha: 0.5,
                    duration: 300,
                    yoyo: true,
                    repeat: 2,
                    onComplete: function() {
                        // 闪烁结束后，执行淡出动画
                        // 获取方块位置信息（在销毁前获取）
                        const row = block.getData('row');
                        const col = block.getData('col');

                        const exitCompleteCallback = function() {
                            // 获取方块ID
                            const blockId = block.getData('id');

                            // 延迟一段时间后，让方块消失
                            setTimeout(() => {
                                // 方块消失，但士兵保持在方块上等待战斗开始
                                // 不再让士兵自动下车

                                // 从blocks数组中移除方块
                                blocks = blocks.filter(b => b !== block);
                                // 更新网格（在销毁前更新）
                                for (let i = 0; i < height; i++) {
                                    for (let j = 0; j < width; j++) {
                                        if (grid[row + i] && grid[row + i][col + j] !== undefined) {
                                            grid[row + i][col + j] = 0;
                                        }
                                    }
                                }
                                // 销毁方块
                                block.destroy();
                                // 检查是否过关
                                checkWinCondition();
                            }, 1000); // 1秒后方块消失
                        };

                        // 方块直接消失，不需要飞出去
                        gameScene.tweens.add({
                            targets: block,
                            alpha: 0,
                            scaleX: 0.8,
                            scaleY: 0.8,
                            duration: 300,
                            ease: 'Power2',
                            onComplete: exitCompleteCallback
                        });
                    }
                });
            }
            
            function checkWinCondition() {
                // 检查是否所有方块都已经出去
                if (blocks.length === 0) {
                    // 所有方块都已消失，游戏胜利
                    // 解锁下一关
                    if (currentLevel === maxUnlockedLevel && currentLevel < levels.length - 1) {
                        maxUnlockedLevel = currentLevel + 1;
                        try {
                            localStorage.setItem('huarongdao_maxLevel', maxUnlockedLevel);
                        } catch (e) {
                            console.error('无法访问本地存储:', e);
                        }
                        // 新关卡已解锁，模态框会在下次打开时自动更新
                    }
                    // 延迟一点显示胜利对话框，确保动画完成
                    setTimeout(() => {
                        showWinModal();
                    }, 100);
                }
            }
            // 游戏内UI变量
            let levelSelectModal = null;
            let winModal = null;

            function createUI() {
                // 华容道UI已简化，不显示关卡和步数
                // 所有底部按钮和功能已完全移除
            }

            // 删除了createButton函数，不再需要底部按钮
            // 删除了undoLastMove函数，简化游戏操作

            // 删除了showLevelSelectModal函数，简化游戏界面

            function showWinModal() {
                // 简化过关处理：直接进入下一关或重新开始
                if (currentLevel < levels.length - 1) {
                    // 自动进入下一关
                    setTimeout(() => {
                        currentLevel++;
                        loadLevel(currentLevel);
                    }, 1000); // 1秒后自动进入下一关
                } else {
                    // 最后一关完成，重新开始第一关
                    setTimeout(() => {
                        currentLevel = 0;
                        loadLevel(currentLevel);
                    }, 1000);
                }
            }

            // 删除了createModalButton函数，不再需要模态框按钮

            // 删除了showGameMenu函数，简化游戏界面
            // 所有UI现在都在Phaser游戏引擎内
        };
    </script>
</body>

</html>
