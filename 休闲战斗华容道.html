<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>华容道</title>
    <script src="https://cdn.jsdelivr.net/npm/phaser@3.60.0/dist/phaser.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            overflow: hidden;
            background-color: #000000;
        }

        #game-container {
            width: 100vw;
            height: 100vh;
        }
         canvas {
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            height: 100vh;
        }
    </style>
</head>

<body>
    <div id="game-container"></div>
    <script id="gameLogic">
        window.onload = function () {
            // 游戏尺寸设置
            const gameWidth = 750  // 最大宽度750px
            const gameHeight = 1334 // 最大高度1334px

            // 战斗系统变量
            let monsters = [], allyBlocks = [];
            let castleHealth = 500, maxCastleHealth = 500;
            let currentBattleLevel = 1, currentWave = 1;
            let battleGrid = [];
            let battleState = 'waiting'; // 改为等待状态，需要点击开始战斗
            let battleTimer = 0;
            let waveTimer = 0;
            let monstersKilled = 0;
            let totalMonstersInWave = 3;
            let battleStarted = false; // 新增：战斗是否已开始

            // 3选1系统变量
            let playerStats = {
                attackDamage: 15,
                attackSpeed: 2000,
                maxHealth: 100,
                critChance: 0,
                lifeSteal: 0,
                multiShot: 1
            };
            let choiceUI = null;
            let choiceOptions = [];
            let isChoosing = false;
            let choiceCounter = 0;
            // 华容道关卡设计
            const levels = [
                // 
               
                {
                    grid: [
                        [7, 0, 0, 0],
                        [1, 1, 2, 2],
                        [1, 1, 2, 2],
                        [0, 4, 0, 0],
                        [3, 0, 5, 6]
                    ],
                    blockSizes: {
                        1: { width: 2, height: 2 },  // 红色大方块 (blockId=1 -> 红色)
                        2: { width: 2, height: 2 },  // 蓝色大方块 (blockId=2 -> 蓝色)
                        3: { width: 1, height: 1 },  // 绿色小方块 (blockId=3 -> 绿色)
                        4: { width: 1, height: 1 },  // 黄色小方块 (blockId=4 -> 黄色)
                        5: { width: 1, height: 1 },  // 红色小方块 (blockId=5 -> 红色)
                        6: { width: 1, height: 1 },  // 蓝色小方块 (blockId=6 -> 蓝色)
                        7: { width: 1, height: 1 }   // 绿色小方块 (blockId=7 -> 绿色)
                    },
                    exits: [
                        { side: 'top', col: 0, width: 2, color: 0xff6b6b, blockId: 1, multiplier: 2 },    // 红色大出口 x2
                        { side: 'top', col: 2, width: 2, color: 0x4a6fff, blockId: 2, multiplier: 4 },    // 蓝色大出口 x4
                        { side: 'top', col: 0, width: 1, color: 0x51cf66, blockId: 3, multiplier: 2 }, // 绿色出口 x2 (方块3)
                    ]
                }
            ];
            // 方块颜色（只使用4种颜色）
            const blockColors = [
                0xff6b6b, // 红色
                0x4a6fff, // 蓝色
                0x51cf66, // 绿色
                0xfcc419  // 黄色
            ];

            // 根据方块ID获取颜色的函数
            function getBlockColor(blockId) {
                const colorIndex = (blockId - 1) % blockColors.length;
                return blockColors[colorIndex];
            }
            // 游戏状态变量
            let currentLevel = 0;
            let steps = 0;
            let maxUnlockedLevel = 0;
            // 尝试从本地存储加载最大解锁关卡
            try {
                const savedMaxLevel = localStorage.getItem('huarongdao_maxLevel');
                if (savedMaxLevel !== null) {
                    maxUnlockedLevel = parseInt(savedMaxLevel, 10);
                }
            } catch (e) {
                console.error('无法访问本地存储:', e);
            }
            // 创建Phaser游戏配置
            const config = {
                type: Phaser.AUTO,
                width: 750,
                height: 1334,
                parent: 'game-container',
                backgroundColor: '#1a1a2e',
                scene: {
                    preload: preload,
                    create: create,
                    update: update
                },
                scale: {
                    mode: Phaser.Scale.FIT,
                    autoCenter: Phaser.Scale.CENTER_BOTH
                }
            };
            // 创建游戏实例
            const game = new Phaser.Game(config);
            // 游戏变量
            let gameScene;
            let blocks = [];
            let exits = [];
            let cellSize;
            let boardX;
            let boardY;
            let boardWidth;
            let boardHeight;
            let grid;
            let currentBlockSizes;
            // 删除了stepText和levelText变量，简化UI
            let selectedBlock = null;
            let startDragPosition = null;

            function preload() {
                // 不需要预加载纹理，使用emoji和文字
            }
            function create() {
                gameScene = this;

                // 创建战斗系统（上半部分）
                createBattleSystem();

                // 设置华容道游戏布局（下半部分）
                setupGameLayout();
                // 加载当前关卡
                loadLevel(currentLevel);
                // 创建UI元素
                createUI();
            }
            function update(time, delta) {
                // 更新战斗系统
                updateBattleSystem(time, delta);
                // 华容道游戏更新逻辑
            }

            function createBattleSystem() {
                // 添加战斗背景
                const battleAreaHeight = gameHeight * 0.4;
                const battleBg = gameScene.add.rectangle(gameWidth / 2, battleAreaHeight / 2, gameWidth, battleAreaHeight, 0x2c3e50);

                // 不再创建城墙和塔防位置

                // 存储战斗区域信息
                gameScene.battleAreaHeight = battleAreaHeight;

                // 创建战斗UI
                createBattleUI();

                // 不自动创建怪物，等待开始战斗按钮
            }

            function createBattleUI() {
                const battleAreaHeight = gameHeight * 0.4;

                // 城堡血条背景
                gameScene.add.rectangle(gameWidth / 2, 50, 300, 20, 0x333333);
                // 城堡血条
                gameScene.defenseWallHealthBar = gameScene.add.rectangle(gameWidth / 2, 50, 300, 20, 0x4A6FFF);
                gameScene.defenseWallHealthBar.setOrigin(0.5, 0.5);

                // UI文本
                gameScene.battleLevelText = gameScene.add.text(50, 80, `战斗关卡: ${currentBattleLevel}`, {
                    fontSize: '16px',
                    fill: '#ffffff'
                });
                gameScene.battleWaveText = gameScene.add.text(200, 80, `波次: ${currentWave}`, {
                    fontSize: '16px',
                    fill: '#ffffff'
                });
                gameScene.defenseWallHealthText = gameScene.add.text(350, 80, `城堡: ${castleHealth}/${maxCastleHealth}`, {
                    fontSize: '16px',
                    fill: '#ffffff'
                });
                gameScene.allyCountText = gameScene.add.text(550, 80, `守军: 0`, {
                    fontSize: '16px',
                    fill: '#2ecc71'
                });

                // 创建开始战斗按钮
                const startBattleButton = gameScene.add.rectangle(gameWidth / 2, battleAreaHeight - 50, 200, 60, 0x27ae60);
                startBattleButton.setStrokeStyle(3, 0x2ecc71);
                startBattleButton.setInteractive();

                const startBattleText = gameScene.add.text(gameWidth / 2, battleAreaHeight - 50, '开始战斗', {
                    fontSize: '24px',
                    fill: '#ffffff',
                    fontWeight: 'bold'
                }).setOrigin(0.5);

                // 按钮点击事件
                startBattleButton.on('pointerdown', () => {
                    if (!battleStarted) {
                        startBattle();
                        startBattleButton.setVisible(false);
                        startBattleText.setVisible(false);
                    }
                });

                // 按钮悬停效果
                startBattleButton.on('pointerover', () => {
                    startBattleButton.setFillStyle(0x2ecc71);
                });
                startBattleButton.on('pointerout', () => {
                    startBattleButton.setFillStyle(0x27ae60);
                });

                gameScene.startBattleButton = startBattleButton;
                gameScene.startBattleText = startBattleText;

                // 怪物血条容器
                gameScene.monsterHealthBars = [];
            }

            // 新增：开始战斗函数
            function startBattle() {
                battleStarted = true;
                battleState = 'playing';

                // 创建怪物
                createBattleWave();

                // 让所有在方块上的士兵开始出征
                allyBlocks.forEach(soldier => {
                    if (soldier.movementPhase === 'onBlock') {
                        soldier.movementPhase = 'exiting'; // 改为出征状态
                        soldier.waitingForBlockExit = false;
                    }
                });
            }

            // 新增：士兵出征函数
            function startSoldierExit(soldier) {
                soldier.isMoving = true;

                // 找到对应的出口
                const currentLevelData = levels[currentLevel];
                const exit = currentLevelData.exits.find(exit => exit.blockId === soldier.onBlockId);

                if (exit) {
                    // 根据出口倍数复制士兵
                    const multiplier = exit.multiplier || 1;
                    const soldiersToCreate = [];

                    // 创建额外的士兵（倍数效果）
                    for (let i = 1; i < multiplier; i++) {
                        const newSoldier = gameScene.add.rectangle(soldier.x + i * 10, soldier.y + i * 10, 30, 30, soldier.originalColor);
                        newSoldier.setStrokeStyle(2, 0xffffff);

                        const newSoldierText = gameScene.add.text(soldier.x + i * 10, soldier.y + i * 10, soldier.soldierType, {
                            fontSize: '20px'
                        });
                        newSoldierText.setOrigin(0.5, 0.5);

                        // 复制士兵属性
                        newSoldier.soldierText = newSoldierText;
                        newSoldier.health = soldier.health;
                        newSoldier.maxHealth = soldier.maxHealth;
                        newSoldier.attackDamage = soldier.attackDamage;
                        newSoldier.lastAttack = 0;
                        newSoldier.targetMonster = null;
                        newSoldier.isMoving = true;
                        newSoldier.originalColor = soldier.originalColor;
                        newSoldier.soldierType = soldier.soldierType;
                        newSoldier.level = soldier.level;
                        newSoldier.exitSide = soldier.exitSide;
                        newSoldier.hasExitedGate = false;
                        newSoldier.hasReachedGate = false;
                        newSoldier.movementPhase = 'exiting';
                        newSoldier.onBlockId = null;

                        allyBlocks.push(newSoldier);
                        soldiersToCreate.push(newSoldier);
                    }

                    // 所有士兵（包括原始士兵和复制的士兵）都向上移动经过出口
                    const allSoldiers = [soldier, ...soldiersToCreate];

                    allSoldiers.forEach((s, index) => {
                        // 计算出口位置
                        let exitX, exitY;
                        if (exit.side === 'top') {
                            exitX = boardX + exit.col * cellSize + (exit.width * cellSize) / 2;
                            exitY = boardY - cellSize * 0.3;
                        }

                        // 先移动到出口位置
                        gameScene.tweens.add({
                            targets: [s, s.soldierText],
                            x: exitX + (index * 15), // 稍微错开位置
                            y: exitY,
                            duration: 1000,
                            ease: 'Power2',
                            onComplete: () => {
                                // 经过出口后继续向上移动到战斗区域
                                const battleY = gameScene.battleAreaHeight - 100;

                                gameScene.tweens.add({
                                    targets: [s, s.soldierText],
                                    y: battleY,
                                    duration: 800,
                                    ease: 'Power2',
                                    onComplete: () => {
                                        s.movementPhase = 'combat';
                                        s.isMoving = false;
                                        s.onBlockId = null;
                                    }
                                });
                            }
                        });
                    });

                    // 显示倍数效果文字
                    if (multiplier > 1) {
                        const multiplierText = gameScene.add.text(soldier.x, soldier.y - 30, `x${multiplier}!`, {
                            fontSize: '20px',
                            fill: '#FFD700',
                            fontWeight: 'bold'
                        }).setOrigin(0.5);

                        gameScene.tweens.add({
                            targets: multiplierText,
                            alpha: 0,
                            y: multiplierText.y - 30,
                            duration: 2000,
                            onComplete: () => multiplierText.destroy()
                        });
                    }
                }

                soldier.movementPhase = 'exiting';
                soldier.onBlockId = null;
            }

            function createBattleWave() {
                // 清除现有怪物（但保留我方方块）
                monsters.forEach(monster => monster.destroy());
                monsters = [];

                // 创建新怪物 - 从上方生成
                for (let i = 0; i < totalMonstersInWave; i++) {
                    const xPos = 100 + i * 120; // 水平分布
                    const yPos = 30; // 从顶部生成

                    // 随机选择怪物emoji
                    const monsterEmojis = ['👹', '👺', '🧌', '👻', '💀', '🧟', '🧛', '🐺', '🦇', '🕷️'];
                    const monsterEmoji = monsterEmojis[Math.floor(Math.random() * monsterEmojis.length)];

                    let monster = gameScene.add.text(xPos, yPos, monsterEmoji, {
                        fontSize: '32px'
                    });
                    monster.setOrigin(0.5, 1);

                    monster.health = 30 + currentBattleLevel * 10;
                    monster.maxHealth = monster.health;
                    monster.lastAttack = 0;
                    monster.isRanged = false;
                    monster.isMoving = false;
                    monster.originalX = xPos;
                    monster.originalY = yPos;
                    monster.jumpTween = null;
                    monster.isNewlySpawned = true; // 标记为新生成
                    monsters.push(monster);

                    // 怪物生成闪动效果
                    monster.setAlpha(0);
                    gameScene.tweens.add({
                        targets: monster,
                        alpha: 1,
                        duration: 300,
                        ease: 'Power2',
                        repeat: 2,
                        yoyo: true,
                        onComplete: () => {
                            monster.setAlpha(1);
                            // 闪动完成后，允许被攻击
                            setTimeout(() => {
                                monster.isNewlySpawned = false;
                            }, 500);
                        }
                    });
                }

                monstersKilled = 0;

                // 重置所有我方方块的目标，让它们重新寻找新的敌人
                allyBlocks.forEach(allyBlock => {
                    allyBlock.targetMonster = null;
                    allyBlock.isMoving = false;
                    // 确保已出城的士兵能够攻击新怪物
                    if (allyBlock.movementPhase === 'defending' || allyBlock.movementPhase === 'combat') {
                        // 重置攻击冷却，让士兵能立即攻击新怪物
                        allyBlock.lastAttack = 0;
                    }
                });
            }

            function updateBattleSystem(time, delta) {
                // 始终更新战斗UI
                updateBattleUI();

                if (battleState === 'playing' && battleStarted) {
                    // 怪物AI
                    updateMonsters(time, delta);

                    // 更新我方方块
                    updateAllyBlocks(time, delta);

                    // 检查波次完成
                    checkBattleWaveComplete();

                    // 检查游戏结束
                    if (castleHealth <= 0) {
                        battleState = 'gameOver';
                        gameScene.add.text(gameWidth / 2, gameHeight * 0.2, '城堡被摧毁！游戏结束', {
                            fontSize: '32px',
                            fill: '#e74c3c',
                            fontFamily: 'Arial'
                        }).setOrigin(0.5);

                        // 显示重新开始按钮
                        const restartButton = gameScene.add.rectangle(gameWidth / 2, gameHeight * 0.3, 200, 60, 0x27ae60);
                        restartButton.setStrokeStyle(3, 0x2ecc71);
                        restartButton.setInteractive();

                        const restartText = gameScene.add.text(gameWidth / 2, gameHeight * 0.3, '重新开始', {
                            fontSize: '24px',
                            fill: '#ffffff',
                            fontWeight: 'bold'
                        }).setOrigin(0.5);

                        restartButton.on('pointerdown', () => {
                            // 重新加载页面
                            location.reload();
                        });
                    }
                }
            }

            // 删除了playerAttack函数，现在只有我方方块攻击

            // 在方块创建时就生成士兵
            function createSoldiersOnBlock(blockContainer, blockId, color, blockX, blockY, blockWidth, blockHeight) {
                // 找到这个方块对应的出口
                const currentLevelData = levels[currentLevel];
                const exitSide = currentLevelData.exits.find(exit => exit.blockId === blockId)?.side || 'bottom';

                // 根据颜色确定士兵类型（同类士兵）
                const soldierTypesByColor = {
                    0xff0000: '⚔️', // 红色 - 剑士
                    0x00ff00: '🛡️', // 绿色 - 盾兵
                    0x0000ff: '🏹', // 蓝色 - 弓箭手
                    0xffff00: '🗡️', // 黄色 - 刀兵
                    0xff00ff: '🔥', // 紫色 - 火法师
                    0x00ffff: '❄️', // 青色 - 冰法师
                    0xffa500: '⚡', // 橙色 - 雷法师
                    0x800080: '💀'  // 深紫 - 死灵法师
                };

                const soldierEmoji = soldierTypesByColor[color] || '⚔️'; // 默认剑士

                // 在方块中心生成4个相同类型的士兵
                const centerX = blockX + blockWidth / 2;
                const centerY = blockY + blockHeight / 2;

                for (let i = 0; i < 4; i++) {
                    // 2x2排列，更紧密
                    const x = centerX + (i % 2) * 20 - 10;
                    const y = centerY + Math.floor(i / 2) * 20 - 10;

                    // 创建我方方块
                    const allyBlock = gameScene.add.rectangle(x, y, 30, 30, color);
                    allyBlock.setStrokeStyle(2, 0xffffff);

                    // 添加相同类型的士兵emoji
                    const soldierText = gameScene.add.text(x, y, soldierEmoji, {
                        fontSize: '20px'
                    });
                    soldierText.setOrigin(0.5, 0.5);

                    // 将emoji绑定到方块
                    allyBlock.soldierText = soldierText;

                    // 设置我方方块属性
                    allyBlock.health = 50;
                    allyBlock.maxHealth = 50;
                    allyBlock.attackDamage = 15;
                    allyBlock.lastAttack = 0;
                    allyBlock.targetMonster = null;
                    allyBlock.isMoving = false;
                    allyBlock.originalColor = color;
                    allyBlock.soldierType = soldierEmoji; // 士兵类型
                    allyBlock.level = 1; // 士兵等级，初始为1级
                    allyBlock.exitSide = exitSide; // 出城方向
                    allyBlock.hasExitedGate = false; // 是否已经出城门
                    allyBlock.hasReachedGate = false; // 是否已经到达城门
                    allyBlock.movementPhase = 'onBlock'; // 在方块上，等待战斗开始
                    allyBlock.waitingForBlockExit = false; // 不等待方块出去，等待战斗开始
                    allyBlock.onBlockId = blockId; // 记录站在哪个方块上
                    allyBlock.blockContainer = blockContainer; // 记录方块容器

                    // 添加到我方方块数组
                    allyBlocks.push(allyBlock);

                    // 创建入场动画
                    allyBlock.setScale(0);
                    soldierText.setScale(0);
                    gameScene.tweens.add({
                        targets: [allyBlock, soldierText],
                        scaleX: 1,
                        scaleY: 1,
                        duration: 500,
                        ease: 'Back.easeOut'
                    });
                }
            }

            function createAllyBlocksOnBlock(color, count, exitSide, spawnX, spawnY, blockId) {
                for (let i = 0; i < count; i++) {
                    // 在华容道方块上生成（稍微分散避免重叠）
                    const x = spawnX + (i % 2) * 20 - 10; // 2x2排列，更紧密
                    const y = spawnY + Math.floor(i / 2) * 20 - 10;

                    // 创建我方方块
                    const allyBlock = gameScene.add.rectangle(x, y, 30, 30, color);
                    allyBlock.setStrokeStyle(2, 0xffffff);

                    // 添加士兵emoji
                    const soldierEmojis = ['⚔️', '🛡️', '🏹', '🗡️'];
                    const soldierEmoji = soldierEmojis[i % soldierEmojis.length];
                    const soldierText = gameScene.add.text(x, y, soldierEmoji, {
                        fontSize: '20px'
                    });
                    soldierText.setOrigin(0.5, 0.5);

                    // 将emoji绑定到方块
                    allyBlock.soldierText = soldierText;

                    // 设置我方方块属性
                    allyBlock.health = 50;
                    allyBlock.maxHealth = 50;
                    allyBlock.attackDamage = 15;
                    allyBlock.lastAttack = 0;
                    allyBlock.targetMonster = null;
                    allyBlock.isMoving = false;
                    allyBlock.originalColor = color;
                    allyBlock.exitSide = exitSide; // 出城方向
                    allyBlock.hasExitedGate = false; // 是否已经出城门
                    allyBlock.hasReachedGate = false; // 是否已经到达城门
                    allyBlock.movementPhase = 'waiting'; // 等待方块出去后开始移动
                    allyBlock.waitingForBlockExit = true; // 等待方块出去
                    allyBlock.onBlockId = blockId; // 记录站在哪个方块上
                    allyBlock.exitSide = exitSide; // 记录方块的出口方向

                    // 添加到我方方块数组
                    allyBlocks.push(allyBlock);

                    // 创建入场动画
                    allyBlock.setScale(0);
                    soldierText.setScale(0);
                    gameScene.tweens.add({
                        targets: [allyBlock, soldierText],
                        scaleX: 1,
                        scaleY: 1,
                        duration: 500,
                        ease: 'Back.easeOut'
                    });

                    // 显示创建提示
                    const createText = gameScene.add.text(x, y - 25, '+1 守军', {
                        fontSize: '10px',
                        fill: '#2ecc71',
                        fontFamily: 'Arial',
                        fontWeight: 'bold'
                    }).setOrigin(0.5);

                    gameScene.tweens.add({
                        targets: createText,
                        alpha: 0,
                        y: createText.y - 15,
                        duration: 1500,
                        onComplete: () => createText.destroy()
                    });
                }
            }

            function updateMonsters(time, delta) {
                monsters.forEach((monster, index) => {
                    // 新生成的怪物在闪动期间不行动
                    if (monster.isNewlySpawned) return;

                    const battleAreaHeight = gameScene.battleAreaHeight;
                    const castleY = battleAreaHeight - 20; // 城堡位置在战斗区域底部

                    // 向下移动攻击城堡
                    if (monster.y < castleY - 50 && !monster.isMoving) {
                        monster.isMoving = true;

                        // 向城堡移动
                        const targetX = monster.x + Math.random() * 60 - 30; // 稍微偏移
                        const targetY = castleY - 40; // 移动到城堡附近

                        gameScene.tweens.add({
                            targets: monster,
                            x: targetX,
                            y: targetY,
                            duration: 4000,
                            ease: 'Power2',
                            onComplete: () => {
                                monster.isMoving = false;
                            }
                        });
                    }

                    // 计算到城堡的距离
                    const distanceToCastle = Phaser.Math.Distance.Between(monster.x, monster.y, gameWidth / 2, castleY);

                    // 攻击城堡
                    if (distanceToCastle <= 120 && time - monster.lastAttack > 2000) {
                        let damage = 12 + currentBattleLevel;
                        castleHealth -= damage;
                        monster.lastAttack = time;

                        // 攻击动画
                        gameScene.tweens.add({
                            targets: monster,
                            rotation: 0.4,
                            duration: 150,
                            yoyo: true,
                            ease: 'Power2'
                        });

                        // 城堡受击效果（血条闪红）
                        gameScene.tweens.add({
                            targets: gameScene.defenseWallHealthBar,
                            fillColor: 0xff0000,
                            duration: 100,
                            yoyo: true,
                            onComplete: () => {
                                const healthPercent = castleHealth / maxCastleHealth;
                                gameScene.defenseWallHealthBar.fillColor = healthPercent > 0.5 ? 0x4A6FFF :
                                                                    healthPercent > 0.25 ? 0xf39c12 : 0xe74c3c;
                            }
                        });
                    }

                    // 如果怪物到达城堡附近但没有在攻击，强制开始攻击
                    else if (monster.y >= castleY - 60 && time - monster.lastAttack > 3000) {
                        let damage = 8 + currentBattleLevel;
                        castleHealth -= damage;
                        monster.lastAttack = time;

                        // 简单攻击动画
                        gameScene.tweens.add({
                            targets: monster,
                            scaleX: 1.2,
                            scaleY: 1.2,
                            duration: 100,
                            yoyo: true
                        });
                    }
                });
            }

            function updateAllyBlocks(time, delta) {
                allyBlocks.forEach((allyBlock, index) => {
                    // 如果战斗未开始，士兵保持在方块上
                    if (!battleStarted && allyBlock.movementPhase === 'onBlock') {
                        return;
                    }

                    // 战斗开始后，处理出征状态的士兵
                    if (allyBlock.movementPhase === 'exiting' && !allyBlock.isMoving) {
                        startSoldierExit(allyBlock);
                        return;
                    }

                    // 跳过在方块上的士兵（如果战斗还未开始）
                    if (allyBlock.movementPhase === 'onBlock' || allyBlock.movementPhase === 'waiting') {
                        return;
                    }

                    // 战斗模式：在战斗区域内攻击怪物
                    if (allyBlock.movementPhase === 'combat') {
                        // 寻找攻击范围内的怪物
                        let nearestMonster = null;
                        let nearestDistance = Infinity;
                        const attackRange = 150; // 攻击范围

                        monsters.forEach(monster => {
                            if (monster.isNewlySpawned) return;

                            const distance = Phaser.Math.Distance.Between(
                                allyBlock.x, allyBlock.y, monster.x, monster.y
                            );
                            if (distance < nearestDistance && distance < attackRange) {
                                nearestDistance = distance;
                                nearestMonster = monster;
                            }
                        });

                        allyBlock.targetMonster = nearestMonster;

                        // 如果有目标且在攻击范围内，进行攻击
                        if (nearestMonster && nearestDistance <= attackRange && time - allyBlock.lastAttack > 1000) {
                            nearestMonster.health -= allyBlock.attackDamage;
                            allyBlock.lastAttack = time;

                            // 攻击动画
                            gameScene.tweens.add({
                                targets: allyBlock,
                                scaleX: 1.3,
                                scaleY: 1.3,
                                duration: 100,
                                yoyo: true
                            });

                            // 攻击特效
                            const attackLine = gameScene.add.line(0, 0,
                                allyBlock.x, allyBlock.y,
                                nearestMonster.x, nearestMonster.y,
                                0xFFFF00, 0.8
                            );
                            attackLine.setLineWidth(3);

                            gameScene.tweens.add({
                                targets: attackLine,
                                alpha: 0,
                                duration: 200,
                                onComplete: () => attackLine.destroy()
                            });

                            // 目标受击效果
                            gameScene.tweens.add({
                                targets: nearestMonster,
                                tint: 0xffffff,
                                duration: 100,
                                yoyo: true,
                                onComplete: () => {
                                    if (nearestMonster.health > 0) {
                                        nearestMonster.clearTint();
                                    }
                                }
                            });

                            // 检查目标是否死亡
                            if (nearestMonster.health <= 0) {
                                nearestMonster.destroy();
                                const monsterIndex = monsters.indexOf(nearestMonster);
                                if (monsterIndex > -1) {
                                    monsters.splice(monsterIndex, 1);
                                    monstersKilled++;
                                }
                                allyBlock.targetMonster = null;
                            }
                        }

                        // 如果没有目标或目标太远，向怪物移动
                        if (!nearestMonster || nearestDistance > attackRange) {
                            if (!allyBlock.isMoving && nearestMonster && nearestDistance > attackRange) {
                                allyBlock.isMoving = true;

                                // 向目标移动
                                const moveDistance = 50;
                                const angle = Phaser.Math.Angle.Between(allyBlock.x, allyBlock.y, nearestMonster.x, nearestMonster.y);
                                const targetX = allyBlock.x + Math.cos(angle) * moveDistance;
                                const targetY = allyBlock.y + Math.sin(angle) * moveDistance;

                                gameScene.tweens.add({
                                    targets: [allyBlock, allyBlock.soldierText],
                                    x: targetX,
                                    y: targetY,
                                    duration: 1000,
                                    ease: 'Power2',
                                    onComplete: () => {
                                        allyBlock.isMoving = false;
                                    }
                                });
                            }
                        }

                        return; // 战斗模式不需要其他移动逻辑
                    }

                    // 简化的目标寻找逻辑（仅用于战斗模式）
                    if (allyBlock.movementPhase === 'combat' && (!allyBlock.targetMonster ||
                        allyBlock.targetMonster.health <= 0 ||
                        !monsters.includes(allyBlock.targetMonster))) {

                        let nearestMonster = null;
                        let nearestDistance = Infinity;

                        monsters.forEach(monster => {
                            if (monster.isNewlySpawned) return;

                            const distance = Phaser.Math.Distance.Between(
                                allyBlock.x, allyBlock.y, monster.x, monster.y
                            );
                            if (distance < nearestDistance && distance < 300) {
                                nearestDistance = distance;
                                nearestMonster = monster;
                            }
                        });

                        allyBlock.targetMonster = nearestMonster;
                    }

                    // 删除复杂的移动逻辑，现在士兵直接在战斗区域内战斗
                });

                // 强化防漏怪机制，特别关注边缘怪物
                monsters.forEach(monster => {
                    if (monster.isNewlySpawned) return;

                    // 检查是否有士兵正在攻击这个怪物
                    const attackingSoldiers = allyBlocks.filter(soldier =>
                        soldier.targetMonster === monster &&
                        (soldier.movementPhase === 'defending' || soldier.movementPhase === 'combat')
                    );

                    // 如果没有士兵攻击这个怪物，强制分配士兵
                    if (attackingSoldiers.length === 0) {
                        let bestSoldier = null;
                        let bestScore = Infinity;

                        allyBlocks.forEach(soldier => {
                            if (soldier.movementPhase === 'defending' || soldier.movementPhase === 'combat') {
                                const distance = Phaser.Math.Distance.Between(
                                    soldier.x, soldier.y, monster.x, monster.y
                                );

                                // 计算分配优先级（距离越近，当前目标越远，优先级越高）
                                let score = distance;

                                // 如果士兵没有目标，优先级最高
                                if (!soldier.targetMonster || !monsters.includes(soldier.targetMonster)) {
                                    score = distance * 0.1; // 大幅降低分数（提高优先级）
                                } else {
                                    // 如果士兵有目标，但目标距离很远，也可以重新分配
                                    const currentTargetDistance = Phaser.Math.Distance.Between(
                                        soldier.x, soldier.y, soldier.targetMonster.x, soldier.targetMonster.y
                                    );
                                    if (currentTargetDistance > distance * 1.5) {
                                        score = distance * 0.5; // 适度降低分数
                                    }
                                }

                                if (score < bestScore) {
                                    bestScore = score;
                                    bestSoldier = soldier;
                                }
                            }
                        });

                        if (bestSoldier) {
                            console.log('为怪物分配士兵:', monster.x, monster.y, '士兵:', bestSoldier.x, bestSoldier.y);
                            bestSoldier.targetMonster = monster;
                            bestSoldier.lastAttack = 0; // 重置攻击冷却
                        }
                    }
                });

                // 额外检查：确保左边缘的怪物不被遗漏
                const leftEdgeMonsters = monsters.filter(monster =>
                    !monster.isNewlySpawned && monster.x < gameWidth * 0.3
                );

                leftEdgeMonsters.forEach(monster => {
                    const attackingSoldiers = allyBlocks.filter(soldier =>
                        soldier.targetMonster === monster &&
                        (soldier.movementPhase === 'defending' || soldier.movementPhase === 'combat')
                    );

                    if (attackingSoldiers.length === 0) {
                        // 强制为左边缘怪物分配士兵
                        const availableSoldiers = allyBlocks.filter(soldier =>
                            soldier.movementPhase === 'defending' || soldier.movementPhase === 'combat'
                        );

                        if (availableSoldiers.length > 0) {
                            const randomSoldier = availableSoldiers[Math.floor(Math.random() * availableSoldiers.length)];
                            console.log('强制为左边缘怪物分配士兵:', monster.x, monster.y);
                            randomSoldier.targetMonster = monster;
                            randomSoldier.lastAttack = 0;
                        }
                    }
                });

                // 清理死亡的我方方块（如果需要的话）
                allyBlocks = allyBlocks.filter(allyBlock => {
                    if (allyBlock.health <= 0) {
                        if (allyBlock.soldierText) {
                            allyBlock.soldierText.destroy();
                        }
                        allyBlock.destroy();
                        return false;
                    }
                    return true;
                });
            }

            function updateBattleUI() {
                // 更新防御墙血条
                const healthPercent = castleHealth / maxCastleHealth;
                gameScene.defenseWallHealthBar.scaleX = healthPercent;
                gameScene.defenseWallHealthBar.x = (gameWidth / 2) - 150 + (300 * healthPercent) / 2;
                gameScene.defenseWallHealthBar.fillColor = healthPercent > 0.5 ? 0x4A6FFF :
                                                    healthPercent > 0.25 ? 0xf39c12 : 0xe74c3c;

                // 更新文本
                gameScene.battleLevelText.setText(`战斗关卡: ${currentBattleLevel}`);
                gameScene.battleWaveText.setText(`波次: ${currentWave}`);
                gameScene.defenseWallHealthText.setText(`城堡: ${Math.max(0, castleHealth)}/${maxCastleHealth}`);
                gameScene.allyCountText.setText(`守军: ${allyBlocks.length}`);

                // 更新怪物血条
                gameScene.monsterHealthBars.forEach(bar => bar.destroy());
                gameScene.monsterHealthBars = [];

                monsters.forEach((monster, index) => {
                    const healthPercent = monster.health / monster.maxHealth;

                    // 血条背景
                    const barBg = gameScene.add.rectangle(
                        monster.x,
                        monster.y - 40,
                        30,
                        4,
                        0x333333
                    );
                    gameScene.monsterHealthBars.push(barBg);

                    // 血条前景
                    const bar = gameScene.add.rectangle(
                        monster.x - 15 + (30 * healthPercent) / 2,
                        monster.y - 40,
                        30 * healthPercent,
                        4,
                        healthPercent > 0.5 ? 0x27ae60 : 0xe74c3c
                    );
                    gameScene.monsterHealthBars.push(bar);
                });

                // 显示我方方块血条
                allyBlocks.forEach((allyBlock, index) => {
                    const healthPercent = allyBlock.health / allyBlock.maxHealth;

                    // 血条背景
                    const barBg = gameScene.add.rectangle(
                        allyBlock.x,
                        allyBlock.y - 20,
                        20,
                        3,
                        0x333333
                    );
                    gameScene.monsterHealthBars.push(barBg);

                    // 血条前景
                    const bar = gameScene.add.rectangle(
                        allyBlock.x - 10 + (20 * healthPercent) / 2,
                        allyBlock.y - 20,
                        20 * healthPercent,
                        3,
                        0x2ecc71 // 绿色表示我方
                    );
                    gameScene.monsterHealthBars.push(bar);
                });
            }

            function checkBattleWaveComplete() {
                if (monsters.length === 0 && !isChoosing) {
                    currentWave++;

                    if (currentWave > 3) {
                        currentBattleLevel++;
                        currentWave = 1;
                        totalMonstersInWave = Math.min(6, 3 + Math.floor(currentBattleLevel / 2));
                    }

                    // 创建新波次
                    setTimeout(() => {
                        createBattleWave();
                    }, 1000);
                }
            }
            function setupGameLayout() {
                // 华容道区域在下半部分，战斗系统占用上半部分
                const battleAreaHeight = gameHeight * 0.4; // 战斗系统占40%
                const headerHeight = gameHeight * 0.05; // 华容道标题区域
                const footerHeight = gameHeight * 0.05; // 底部空间
                boardHeight = gameHeight - battleAreaHeight - headerHeight - footerHeight;
                // 华容道是4列5行的网格
                const cols = 4;
                const rows = 5;
                // 计算单元格大小，确保游戏板能完全适应屏幕，增加利用率
                cellSize = Math.min(gameWidth / cols, boardHeight / rows) * 0.90;
                // 计算游戏板的宽度和位置，使其居中
                boardWidth = cellSize * cols;
                boardX = (gameWidth - boardWidth) / 2;
                boardY = battleAreaHeight + headerHeight + (boardHeight - cellSize * rows) / 2;

                // 绘制华容道游戏板背景
                gameScene.add.rectangle(gameWidth / 2, battleAreaHeight + headerHeight + boardHeight / 2, boardWidth + cellSize * 0.1, cellSize * rows + cellSize * 0.1, 0xEEEEEE)
                   .setOrigin(0.5, 0.5)
                   .setStrokeStyle(2, 0xDDDDDD);

                // 不再绘制马路和城墙系统
            }
            function loadLevel(levelIndex) {
                // 清除现有方块和出口
                blocks.forEach(block => block.destroy());
                blocks = [];
                exits.forEach(exit => {
                    exit.graphics.destroy();
                    if (exit.multiplierText) {
                        exit.multiplierText.destroy();
                    }
                });
                exits = [];
                // 重置步数
                steps = 0;
                // 重置战斗状态
                battleStarted = false;
                battleState = 'waiting';
                // 清除所有士兵
                allyBlocks.forEach(soldier => {
                    soldier.destroy();
                    if (soldier.soldierText) {
                        soldier.soldierText.destroy();
                    }
                });
                allyBlocks = [];
                // 获取当前关卡数据
                const levelData = levels[levelIndex];
                grid = JSON.parse(JSON.stringify(levelData.grid)); // 深拷贝网格
                currentBlockSizes = levelData.blockSizes;
                // 创建出口
                createExits(levelData.exits);
                // 创建方块
                createBlocks();
                // 显示开始战斗按钮
                if (gameScene.startBattleButton) {
                    gameScene.startBattleButton.setVisible(true);
                    gameScene.startBattleText.setVisible(true);
                }
                // UI已简化，不更新华容道相关文本
            }
            function createExits(exitData) {
                exitData.forEach(exit => {
                    const { side, col, row, width, color, multiplier } = exit;
                    let x, y, exitWidth, exitHeight;

                    if (side === 'top') {
                        x = boardX + col * cellSize;
                        y = boardY - cellSize * 0.3; // 出口更窄
                        exitWidth = width * cellSize;
                        exitHeight = cellSize * 0.3;
                    } else if (side === 'bottom') {
                        x = boardX + col * cellSize;
                        y = boardY + 5 * cellSize;
                        exitWidth = width * cellSize;
                        exitHeight = cellSize * 0.3;
                    } else if (side === 'left') {
                        x = boardX - cellSize * 0.3;
                        y = boardY + row * cellSize;
                        exitWidth = cellSize * 0.3;
                        exitHeight = width * cellSize; // 这里width表示高度
                    } else if (side === 'right') {
                        x = boardX + 4 * cellSize;
                        y = boardY + row * cellSize;
                        exitWidth = cellSize * 0.3;
                        exitHeight = width * cellSize;
                    }

                    const exitBlock = gameScene.add.graphics();
                    exitBlock.fillStyle(color, 1);
                    exitBlock.fillRect(x, y, exitWidth, exitHeight);

                    // 添加倍数文字显示
                    const multiplierText = gameScene.add.text(
                        x + exitWidth / 2,
                        y + exitHeight / 2,
                        `x${multiplier}`,
                        {
                            fontSize: '18px',
                            fill: '#ffffff',
                            fontWeight: 'bold',
                            stroke: '#000000',
                            strokeThickness: 2
                        }
                    ).setOrigin(0.5);

                    exits.push({
                        graphics: exitBlock,
                        multiplierText: multiplierText,
                        side,
                        col: col || 0,
                        row: row || 0,
                        width: width || 1,
                        color,
                        blockId: exit.blockId,
                        multiplier: multiplier || 1
                    });
                });
            }
            function createBlocks() {
                // 创建方块对象
                const blockIds = new Set();
                // 首先找出所有方块ID
                for (let row = 0; row < grid.length; row++) {
                    for (let col = 0; col < grid[row].length; col++) {
                        const blockId = grid[row][col];
                        if (blockId > 0) {
                            blockIds.add(blockId);
                        }
                    }
                }
                // 为每个方块ID创建方块
                blockIds.forEach(blockId => {
                    // 找到方块的起始位置
                    let startRow = -1, startCol = -1;
                    outerLoop: for (let row = 0; row < grid.length; row++) {
                        for (let col = 0; col < grid[row].length; col++) {
                            if (grid[row][col] === blockId) {
                                startRow = row;
                                startCol = col;
                                break outerLoop;
                            }
                        }
                    }
                    if (startRow >= 0 && startCol >= 0) {
                        const blockSize = currentBlockSizes[blockId];
                        const width = blockSize.width;
                        const height = blockSize.height;
                        // 计算方块的像素位置和大小
                        const x = boardX + startCol * cellSize;
                        const y = boardY + startRow * cellSize;
                        const blockWidth = width * cellSize;
                        const blockHeight = height * cellSize;
                        // 选择方块颜色
                        const colorIndex = (blockId - 1) % blockColors.length;
                        const color = blockColors[colorIndex];
                        // 创建方块图形
                        const block = gameScene.add.graphics();
                        // 绘制方块
                        block.fillStyle(color, 1);
                        block.lineStyle(2, 0xffffff, 1);
                        block.fillRoundedRect(0, 0, blockWidth - 4, blockHeight - 4, 10);
                        block.strokeRoundedRect(0, 0, blockWidth - 4, blockHeight - 4, 10);
                        // 创建方块容器
                        const blockContainer = gameScene.add.container(x + 2, y + 2);
                        blockContainer.add(block);
                        // 存储方块数据
                        blockContainer.setData('id', blockId);
                        blockContainer.setData('row', startRow);
                        blockContainer.setData('col', startCol);
                        blockContainer.setData('width', width);
                        blockContainer.setData('height', height);
                        blockContainer.setData('color', color);
                        // 使方块可交互
                        blockContainer.setInteractive(new Phaser.Geom.Rectangle(0, 0, blockWidth - 4, blockHeight - 4), Phaser.Geom.Rectangle.Contains);
                        // 添加拖动事件
                        gameScene.input.setDraggable(blockContainer);
                        // 将方块添加到数组
                        blocks.push(blockContainer);

                        // 在方块创建时就生成士兵
                        createSoldiersOnBlock(blockContainer, blockId, color, x, y, blockWidth, blockHeight);
                    }
                });
                // 设置拖动事件
                gameScene.input.on('dragstart', function (pointer, gameObject) {
                    selectedBlock = gameObject;
                    startDragPosition = { x: pointer.x, y: pointer.y };
                });
                gameScene.input.on('drag', function (pointer, gameObject, dragX, dragY) {
                    // 不直接更新位置，只记录拖动方向
                });
                gameScene.input.on('dragend', function (pointer, gameObject) {
                    if (startDragPosition) {
                        const dx = pointer.x - startDragPosition.x;
                        const dy = pointer.y - startDragPosition.y;
                        // 确定主要拖动方向
                        if (Math.abs(dx) > Math.abs(dy)) {
                            // 水平拖动
                            if (dx > cellSize / 3) {
                                moveBlockToEnd(gameObject, 'right');
                            } else if (dx < -cellSize / 3) {
                                moveBlockToEnd(gameObject, 'left');
                            }
                        } else {
                            // 垂直拖动
                            if (dy > cellSize / 3) {
                                moveBlockToEnd(gameObject, 'down');
                            } else if (dy < -cellSize / 3) {
                                moveBlockToEnd(gameObject, 'up');
                            }
                        }
                        startDragPosition = null;
                    }
                    selectedBlock = null;
                });
                // 添加点击事件
                gameScene.input.on('gameobjectdown', function (pointer, gameObject) {
                    selectedBlock = gameObject;
                });
            }
            function moveBlockToEnd(block, direction) {
                const blockId = block.getData('id');
                const startRow = block.getData('row');
                const startCol = block.getData('col');
                const width = block.getData('width');
                const height = block.getData('height');

                let newRow = startRow;
                let newCol = startCol;

                // 找到该方向上能移动的最远位置
                let canMove = true;
                while (canMove) {
                    let testRow = newRow;
                    let testCol = newCol;

                    // 根据方向计算下一个测试位置
                    switch (direction) {
                        case 'up':
                            testRow--;
                            break;
                        case 'down':
                            testRow++;
                            break;
                        case 'left':
                            testCol--;
                            break;
                        case 'right':
                            testCol++;
                            break;
                    }

                    // 检查这个位置是否有效
                    if (isValidMove(blockId, startRow, startCol, testRow, testCol, width, height)) {
                        newRow = testRow;
                        newCol = testCol;
                    } else {
                        canMove = false;
                    }
                }

                // 如果找到了新位置，执行移动
                if (newRow !== startRow || newCol !== startCol) {
                    // 更新网格
                    updateGrid(blockId, startRow, startCol, newRow, newCol, width, height);
                    // 更新方块位置
                    block.setData('row', newRow);
                    block.setData('col', newCol);

                    // 移动方块上的士兵
                    moveBlockSoldiers(block, startRow, startCol, newRow, newCol);

                    // 动画移动方块
                    gameScene.tweens.add({
                        targets: block,
                        x: boardX + newCol * cellSize + 2,
                        y: boardY + newRow * cellSize + 2,
                        duration: 300, // 稍微长一点的动画时间
                        ease: 'Power2',
                        onComplete: function() {
                            if (!gameScene || !gameScene.scene || !gameScene.scene.isActive()) return;
                            // 检查方块是否到达出口
                            checkExitCondition(block);
                            // 检查是否过关
                            checkWinCondition();
                        }
                    });
                    // 增加步数（不显示）
                    steps++;
                } else {
                    // 无效移动，播放轻微震动动画
                    gameScene.tweens.add({
                        targets: block,
                        x: block.x + (direction === 'right' ? 5 : (direction === 'left' ? -5 : 0)),
                        y: block.y + (direction === 'down' ? 5 : (direction === 'up' ? -5 : 0)),
                        duration: 50,
                        yoyo: true,
                        repeat: 1
                    });
                }
            }

            // 移动方块上的士兵
            function moveBlockSoldiers(block, startRow, startCol, newRow, newCol) {
                const blockId = block.getData('id');
                const deltaX = (newCol - startCol) * cellSize;
                const deltaY = (newRow - startRow) * cellSize;

                // 找到站在这个方块上的士兵
                allyBlocks.forEach(soldier => {
                    if ((soldier.movementPhase === 'onBlock' || soldier.waitingForBlockExit) && soldier.onBlockId === blockId) {
                        // 移动士兵跟随方块
                        gameScene.tweens.add({
                            targets: [soldier, soldier.soldierText],
                            x: soldier.x + deltaX,
                            y: soldier.y + deltaY,
                            duration: 300,
                            ease: 'Power2'
                        });
                    }
                });
            }

            // 删除了塔防相关函数，现在士兵直接在战斗区域战斗
            function isValidMove(blockId, startRow, startCol, newRow, newCol, width, height) {
                // 检查是否超出边界
                if (newRow < 0 || newCol < 0 || newRow + height > grid.length || newCol + width > grid[0].length) {
                    return false;
                }
                // 检查新位置是否被占用
                for (let r = 0; r < height; r++) {
                    for (let c = 0; c < width; c++) {
                        const currentCell = grid[startRow + r][startCol + c];
                        const newCell = grid[newRow + r][newCol + c];
                        // 如果新位置不是当前方块或空位，则无法移动
                        if (newCell !== 0 && newCell !== blockId) {
                            return false;
                        }
                    }
                }
                return true;
            }
            function updateGrid(blockId, startRow, startCol, newRow, newCol, width, height) {
                // 清除原位置
                for (let r = 0; r < height; r++) {
                    for (let c = 0; c < width; c++) {
                        if (grid[startRow + r][startCol + c] === blockId) {
                            grid[startRow + r][startCol + c] = 0;
                        }
                    }
                }
                // 设置新位置
                for (let r = 0; r < height; r++) {
                    for (let c = 0; c < width; c++) {
                        grid[newRow + r][newCol + c] = blockId;
                    }
                }
            }
            function checkExitCondition(block) {
                const blockId = block.getData('id');
                const row = block.getData('row');
                const col = block.getData('col');
                const width = block.getData('width');
                const height = block.getData('height');
                const blockColor = block.getData('color');
                
                levels[currentLevel].exits.forEach(exit => {
                    const { side, col: exitCol, width: exitWidth, color, blockId: exitBlockId } = exit;

                    // 检查方块是否到达出口位置
                    if (side === 'top' && row === 0) {
                        // 检查方块是否在出口范围内，且方块宽度不超过出口宽度
                        if (col >= exitCol && col + width <= exitCol + exitWidth && width <= exitWidth) {
                            // 检查方块颜色是否与出口匹配
                            if (blockColor === color || blockId === exitBlockId) {
                                // 执行出门动画
                                executeExitAnimation(block, side, exitCol);
                                return;
                            } else {
                                // 颜色不匹配，播放错误动画
                                gameScene.tweens.add({
                                    targets: block,
                                    alpha: 0.5,
                                    duration: 200,
                                    yoyo: true,
                                    repeat: 1
                                });
                                return;
                            }
                        }
                    } else if (side === 'bottom' && row + height === 5) {
                        // 检查方块是否在出口范围内，且方块宽度不超过出口宽度
                        if (col >= exitCol && col + width <= exitCol + exitWidth && width <= exitWidth) {
                            // 检查方块颜色是否与出口匹配
                            if (blockColor === color || blockId === exitBlockId) {
                                // 执行出门动画
                                executeExitAnimation(block, side, exitCol);
                                return;
                            } else {
                                // 颜色不匹配，播放错误动画
                                gameScene.tweens.add({
                                    targets: block,
                                    alpha: 0.5,
                                    duration: 200,
                                    yoyo: true,
                                    repeat: 1
                                });
                                return;
                            }
                        }
                    }
                });
            }
            
            function executeExitAnimation(block, side, exitCol) {
                const blockId = block.getData('id');
                const width = block.getData('width');
                const height = block.getData('height');
                
                // 首先播放闪烁效果
                gameScene.tweens.add({
                    targets: block,
                    alpha: 0.5,
                    duration: 300,
                    yoyo: true,
                    repeat: 2,
                    onComplete: function() {
                        // 闪烁结束后，执行淡出动画
                        // 获取方块位置信息（在销毁前获取）
                        const row = block.getData('row');
                        const col = block.getData('col');

                        const exitCompleteCallback = function() {
                            // 获取方块ID
                            const blockId = block.getData('id');

                            // 延迟一段时间后，让方块消失
                            setTimeout(() => {
                                // 方块消失，但士兵保持在方块上等待战斗开始
                                // 不再让士兵自动下车

                                // 从blocks数组中移除方块
                                blocks = blocks.filter(b => b !== block);
                                // 更新网格（在销毁前更新）
                                for (let i = 0; i < height; i++) {
                                    for (let j = 0; j < width; j++) {
                                        if (grid[row + i] && grid[row + i][col + j] !== undefined) {
                                            grid[row + i][col + j] = 0;
                                        }
                                    }
                                }
                                // 销毁方块
                                block.destroy();
                                // 检查是否过关
                                checkWinCondition();
                            }, 1000); // 1秒后方块消失
                        };

                        // 方块直接消失，不需要飞出去
                        gameScene.tweens.add({
                            targets: block,
                            alpha: 0,
                            scaleX: 0.8,
                            scaleY: 0.8,
                            duration: 300,
                            ease: 'Power2',
                            onComplete: exitCompleteCallback
                        });
                    }
                });
            }
            
            function checkWinCondition() {
                // 检查是否所有方块都已经出去
                if (blocks.length === 0) {
                    // 所有方块都已消失，游戏胜利
                    // 解锁下一关
                    if (currentLevel === maxUnlockedLevel && currentLevel < levels.length - 1) {
                        maxUnlockedLevel = currentLevel + 1;
                        try {
                            localStorage.setItem('huarongdao_maxLevel', maxUnlockedLevel);
                        } catch (e) {
                            console.error('无法访问本地存储:', e);
                        }
                        // 新关卡已解锁，模态框会在下次打开时自动更新
                    }
                    // 延迟一点显示胜利对话框，确保动画完成
                    setTimeout(() => {
                        showWinModal();
                    }, 100);
                }
            }
            // 游戏内UI变量
            let levelSelectModal = null;
            let winModal = null;

            function createUI() {
                // 华容道UI已简化，不显示关卡和步数
                // 所有底部按钮和功能已完全移除
            }

            // 删除了createButton函数，不再需要底部按钮
            // 删除了undoLastMove函数，简化游戏操作

            // 删除了showLevelSelectModal函数，简化游戏界面

            function showWinModal() {
                // 简化过关处理：直接进入下一关或重新开始
                if (currentLevel < levels.length - 1) {
                    // 自动进入下一关
                    setTimeout(() => {
                        currentLevel++;
                        loadLevel(currentLevel);
                    }, 1000); // 1秒后自动进入下一关
                } else {
                    // 最后一关完成，重新开始第一关
                    setTimeout(() => {
                        currentLevel = 0;
                        loadLevel(currentLevel);
                    }, 1000);
                }
            }

            // 删除了createModalButton函数，不再需要模态框按钮

            // 删除了showGameMenu函数，简化游戏界面
            // 所有UI现在都在Phaser游戏引擎内
        };
    </script>
</body>

</html>
